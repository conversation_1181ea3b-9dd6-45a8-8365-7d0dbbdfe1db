{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:mx", "is_spawnable": true, "is_summonable": true, "properties": {"ditsh:playing_music": {"type": "bool", "client_sync": false, "default": false}, "ditsh:jumping": {"type": "bool", "client_sync": true, "default": false}, "ditsh:dashing": {"type": "bool", "client_sync": true, "default": false}, "ditsh:ground_check": {"type": "bool", "client_sync": true, "default": false}}, "animations": {"attack_type_controller": "controller.animation.ithiefi_ditsh_mx.attack_type", "ground_check_controller": "controller.animation.ithiefi_ditsh_mx.ground_check"}, "scripts": {"animate": ["attack_type_controller", {"ground_check_controller": "q.property('ditsh:ground_check') == true"}]}}, "component_groups": {"ditsh:music_playing": {"minecraft:timer": {"looping": true, "time": 9.3, "time_down_event": {"event": "ditsh:maintain_chase_music"}}}, "ditsh:default_attack": {"minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true, "horizontal_reach": 0.8, "cooldown_time": 1, "can_spread_on_fire": true, "speed_multiplier": 1.45, "on_kill": {"event": "ditsh:on_kill", "target": "self"}}}, "ditsh:dash_attack": {"minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true, "horizontal_reach": 0.8, "cooldown_time": 1, "can_spread_on_fire": true, "speed_multiplier": 2.45, "on_attack": {"event": "ditsh:on_dash_attack", "target": "self"}, "on_kill": {"event": "ditsh:on_kill", "target": "self"}}, "minecraft:scheduler": {"min_delay_secs": 0, "max_delay_secs": 0, "scheduled_events": [{"filters": {"test": "bool_property", "subject": "self", "domain": "ditsh:dashing", "value": true}, "event": {"event": "ditsh:mx_check_for_wall_hit", "target": "self"}}]}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ditsh:default_attack"]}, "queue_command": {"command": "playsound mob.ditsh.mx.spawn @a ~ ~ ~"}}, "ditsh:on_death": {}, "ditsh:on_kill": {"queue_command": {"command": "playsound mob.ditsh.mx.kill @a ~ ~ ~"}}, "ditsh:start_chase_music": {"add": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": true}}, "ditsh:stop_chase_music": {"remove": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": false, "ditsh:dashing": false, "ditsh:jumping": false}}, "ditsh:maintain_chase_music": {}, "ditsh:select_attack": {"randomize": [{"weight": 80}, {"weight": 2, "filters": {"test": "bool_property", "subject": "self", "domain": "ditsh:dashing", "value": false}, "set_property": {"ditsh:dashing": true}, "trigger": "ditsh:mx_dash"}, {"weight": 2, "filters": {"test": "bool_property", "subject": "self", "domain": "ditsh:jumping", "value": false}, "set_property": {"ditsh:jumping": true}, "trigger": "ditsh:mx_jump"}]}, "ditsh:mx_dash": {"sequence": [{"remove": {"component_groups": ["ditsh:default_attack"]}}, {"add": {"component_groups": ["ditsh:dash_attack"]}}]}, "ditsh:on_dash_attack": {"sequence": [{"set_property": {"ditsh:dashing": false}}, {"queue_command": {"command": "playsound mob.ditsh.mx.hit_wall @a ~ ~ ~"}}, {"remove": {"component_groups": ["ditsh:dash_attack"]}}, {"add": {"component_groups": ["ditsh:default_attack"]}}]}, "ditsh:mx_check_for_wall_hit": {}, "ditsh:mx_jump": {}, "ditsh:on_ground_landing": {"set_property": {"ditsh:jumping": false, "ditsh:ground_check": false}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 12 + (query.equipment_count * Math.Random(16,24)) : 0"}, "minecraft:type_family": {"family": ["ditsh", "mx", "mob", "monster"]}, "minecraft:collision_box": {"width": 0.9, "height": 2.9}, "minecraft:health": {"value": 80, "max": 80}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:movement": {"value": 0.2}, "minecraft:movement.basic": {}, "minecraft:navigation.walk": {"can_open_doors": true, "can_open_iron_doors": true, "avoid_damage_blocks": true, "avoid_water": false, "can_path_over_water": true, "can_path_over_lava": true, "can_sink": false, "is_amphibious": true}, "minecraft:knockback_resistance": {"value": 0.7, "max": 1}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 0, "target_search_height": -1, "reselect_targets": true, "cooldown": 0, "attack_interval": 0, "must_reach": false, "must_see": false, "reevaluate_description": true, "entity_types": [{"priority": 0, "filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "max_dist": 256}]}, "minecraft:behavior.hurt_by_target": {"priority": 1, "entity_types": {"filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "reevaluate_description": false, "max_dist": 256}}, "minecraft:behavior.random_look_around": {"priority": 5}, "minecraft:behavior.random_stroll": {"priority": 4}, "minecraft:behavior.float": {"priority": 0}, "minecraft:attack": {"damage": 7}, "minecraft:follow_range": {"value": 1024, "max": 1024}, "minecraft:jump.static": {}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:on_death": {"event": "ditsh:on_death", "target": "self"}, "minecraft:environment_sensor": {"triggers": [{"event": "ditsh:start_chase_music", "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": true}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": false}]}}, {"event": "ditsh:stop_chase_music", "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": false}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": true}]}}]}, "minecraft:damage_sensor": {"triggers": [{"cause": "fall", "deals_damage": "no"}]}, "minecraft:pushable": {}}}}